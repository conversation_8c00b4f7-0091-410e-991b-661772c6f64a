import { test, expect } from '@playwright/test';

// 測試配置
const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:8080';

// 測試用戶信息
const TEST_USER = {
  email: '<EMAIL>',
  password: 'test123'
};

// 測試商品信息
const TEST_PRODUCT = {
  name: '測試商品',
  price: 99.99
};

// 測試收貨信息
const TEST_ADDRESS = {
  receiverName: '張三',
  receiverPhone: '13800138000',
  receiverAddress: '北京市朝陽區測試街道123號'
};

test.describe('購物車和支付功能測試', () => {
  
  test.beforeEach(async ({ page }) => {
    // 每個測試前都導航到首頁
    await page.goto(BASE_URL);
  });

  test('用戶登錄', async ({ page }) => {
    // 點擊登錄按鈕
    await page.click('text=登錄');
    
    // 填寫登錄信息
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    
    // 提交登錄
    await page.click('button[type="submit"]');
    
    // 驗證登錄成功
    await expect(page).toHaveURL(`${BASE_URL}/`);
    await expect(page.locator('text=歡迎')).toBeVisible();
  });

  test('添加商品到購物車', async ({ page }) => {
    // 先登錄
    await loginUser(page);
    
    // 導航到商品頁面
    await page.goto(`${BASE_URL}/products`);
    
    // 等待商品列表加載
    await page.waitForSelector('.product-list');
    
    // 點擊第一個商品
    await page.click('.product-item:first-child');
    
    // 等待商品詳情頁加載
    await page.waitForSelector('.product-detail');
    
    // 點擊加入購物車
    await page.click('button:has-text("加入購物車")');
    
    // 驗證成功提示
    await expect(page.locator('.el-message--success')).toBeVisible();
  });

  test('購物車頁面功能', async ({ page }) => {
    // 先登錄並添加商品到購物車
    await loginUser(page);
    await addProductToCart(page);
    
    // 導航到購物車頁面
    await page.goto(`${BASE_URL}/cart`);
    
    // 等待購物車加載
    await page.waitForSelector('.cart-items');
    
    // 驗證商品存在
    await expect(page.locator('.cart-item')).toHaveCount(1);
    
    // 測試數量調整
    const quantityInput = page.locator('.quantity-input');
    await quantityInput.fill('2');
    await page.keyboard.press('Enter');
    
    // 等待更新完成
    await page.waitForTimeout(1000);
    
    // 驗證數量更新
    await expect(quantityInput).toHaveValue('2');
    
    // 測試選中狀態
    const checkbox = page.locator('.item-checkbox input');
    await expect(checkbox).toBeChecked();
    
    // 取消選中
    await checkbox.uncheck();
    await expect(checkbox).not.toBeChecked();
    
    // 重新選中
    await checkbox.check();
    await expect(checkbox).toBeChecked();
  });

  test('訂單創建流程', async ({ page }) => {
    // 先登錄並添加商品到購物車
    await loginUser(page);
    await addProductToCart(page);
    
    // 導航到購物車頁面
    await page.goto(`${BASE_URL}/cart`);
    
    // 等待購物車加載
    await page.waitForSelector('.cart-items');
    
    // 點擊去結算
    await page.click('button:has-text("去結算")');
    
    // 等待結算頁面加載
    await page.waitForSelector('.checkout-content');
    
    // 填寫收貨信息
    await page.fill('input[placeholder*="收貨人姓名"]', TEST_ADDRESS.receiverName);
    await page.fill('input[placeholder*="聯繫電話"]', TEST_ADDRESS.receiverPhone);
    await page.fill('textarea[placeholder*="詳細收貨地址"]', TEST_ADDRESS.receiverAddress);
    
    // 提交訂單
    await page.click('button:has-text("提交訂單")');
    
    // 等待訂單創建成功
    await page.waitForSelector('.payment-view', { timeout: 10000 });
    
    // 驗證跳轉到支付頁面
    await expect(page).toHaveURL(/\/payment\/\d+/);
    
    // 驗證訂單信息顯示
    await expect(page.locator('.order-info')).toBeVisible();
    await expect(page.locator('text=訂單號')).toBeVisible();
  });

  test('支付頁面功能', async ({ page }) => {
    // 先創建訂單
    await createTestOrder(page);
    
    // 驗證支付頁面元素
    await expect(page.locator('.payment-status')).toBeVisible();
    await expect(page.locator('text=待付款')).toBeVisible();
    await expect(page.locator('button:has-text("立即支付")')).toBeVisible();
    
    // 測試刷新狀態功能
    await page.click('button:has-text("刷新狀態")');
    await expect(page.locator('.el-message--success')).toBeVisible();
    
    // 注意：實際支付測試需要支付寶沙箱環境，這裡只測試UI
    console.log('支付寶支付測試需要沙箱環境，跳過實際支付流程');
  });

  test('訂單管理頁面', async ({ page }) => {
    // 先創建訂單
    await createTestOrder(page);
    
    // 導航到訂單管理頁面
    await page.goto(`${BASE_URL}/orders`);
    
    // 等待訂單列表加載
    await page.waitForSelector('.orders-list');
    
    // 驗證訂單存在
    await expect(page.locator('.order-card')).toHaveCount(1);
    
    // 測試狀態篩選
    await page.click('button:has-text("待付款")');
    await page.waitForTimeout(1000);
    await expect(page.locator('.order-card')).toHaveCount(1);
    
    // 測試查看詳情
    await page.click('button:has-text("查看詳情")');
    await expect(page).toHaveURL(/\/orders\/\d+/);
  });

  test('API 接口測試', async ({ request }) => {
    // 測試獲取購物車 API
    const cartResponse = await request.get(`${API_BASE_URL}/api/cart`, {
      headers: {
        'Authorization': 'Bearer test-token'
      }
    });
    
    console.log('購物車 API 響應狀態:', cartResponse.status());
    
    // 測試獲取商品列表 API
    const productsResponse = await request.get(`${API_BASE_URL}/api/products`);
    console.log('商品列表 API 響應狀態:', productsResponse.status());
  });

});

// 輔助函數
async function loginUser(page: any) {
  await page.goto(`${BASE_URL}/login`);
  await page.fill('input[type="email"]', TEST_USER.email);
  await page.fill('input[type="password"]', TEST_USER.password);
  await page.click('button[type="submit"]');
  await page.waitForURL(`${BASE_URL}/`);
}

async function addProductToCart(page: any) {
  await page.goto(`${BASE_URL}/products`);
  await page.waitForSelector('.product-list');
  await page.click('.product-item:first-child');
  await page.waitForSelector('.product-detail');
  await page.click('button:has-text("加入購物車")');
  await page.waitForSelector('.el-message--success');
}

async function createTestOrder(page: any) {
  await loginUser(page);
  await addProductToCart(page);
  await page.goto(`${BASE_URL}/cart`);
  await page.waitForSelector('.cart-items');
  await page.click('button:has-text("去結算")');
  await page.waitForSelector('.checkout-content');
  await page.fill('input[placeholder*="收貨人姓名"]', TEST_ADDRESS.receiverName);
  await page.fill('input[placeholder*="聯繫電話"]', TEST_ADDRESS.receiverPhone);
  await page.fill('textarea[placeholder*="詳細收貨地址"]', TEST_ADDRESS.receiverAddress);
  await page.click('button:has-text("提交訂單")');
  await page.waitForSelector('.payment-view', { timeout: 10000 });
}
