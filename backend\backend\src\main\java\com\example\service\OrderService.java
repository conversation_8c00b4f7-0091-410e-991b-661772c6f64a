package com.example.service;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.entity.*;
import com.example.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 訂單服務類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@Service
@Transactional
public class OrderService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private OrderItemRepository orderItemRepository;
    
    @Autowired
    private CartRepository cartRepository;
    
    @Autowired
    private CartItemRepository cartItemRepository;
    
    @Autowired
    private ProductRepository productRepository;
    
    @Autowired
    private PaymentRepository paymentRepository;
    
    /**
     * 從購物車創建訂單
     */
    public ApiResponse<Order> createOrderFromCart(Long userId, String receiverName, String receiverPhone, 
                                                 String receiverAddress, String remark) {
        try {
            // 獲取用戶購物車
            Optional<Cart> cartOpt = cartRepository.findByUserIdAndStatus(userId, Cart.Status.ACTIVE);
            if (cartOpt.isEmpty()) {
                return ApiResponse.error("購物車為空");
            }
            
            Cart cart = cartOpt.get();
            List<CartItem> selectedItems = cartItemRepository.findSelectedItemsByCartIdWithProduct(cart.getId());
            
            if (selectedItems.isEmpty()) {
                return ApiResponse.error("請選擇要購買的商品");
            }
            
            // 檢查庫存
            for (CartItem item : selectedItems) {
                Product product = item.getProduct();
                if (product == null || product.getStatus() != Product.Status.ON_SHELF) {
                    return ApiResponse.error("商品 " + item.getProductName() + " 已下架");
                }
                if (product.getStock() < item.getQuantity()) {
                    return ApiResponse.error("商品 " + item.getProductName() + " 庫存不足");
                }
            }
            
            // 計算總金額
            BigDecimal totalAmount = selectedItems.stream()
                    .map(item -> item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 生成訂單號
            String orderNumber = generateOrderNumber();
            
            // 創建訂單
            Order order = new Order(orderNumber, userId, totalAmount, receiverName, receiverPhone, receiverAddress);
            if (remark != null && !remark.trim().isEmpty()) {
                order.setRemark(remark);
            }
            Order savedOrder = orderRepository.save(order);
            
            // 創建訂單項目
            for (CartItem cartItem : selectedItems) {
                Product product = cartItem.getProduct();
                OrderItem orderItem = new OrderItem(
                    savedOrder.getId(),
                    cartItem.getProductId(),
                    cartItem.getProductName(),
                    cartItem.getProductImageUrl(),
                    cartItem.getQuantity(),
                    cartItem.getPrice(),
                    product.getBrand(),
                    product.getModel()
                );
                orderItemRepository.save(orderItem);
                
                // 減少庫存
                product.setStock(product.getStock() - cartItem.getQuantity());
                product.setSoldCount(product.getSoldCount() + cartItem.getQuantity());
                productRepository.save(product);
            }
            
            // 創建支付記錄
            Payment payment = new Payment(savedOrder.getId(), Payment.PaymentMethod.ALIPAY, totalAmount);
            paymentRepository.save(payment);
            
            // 清除購物車選中項目
            cartItemRepository.deleteSelectedItemsByCartId(cart.getId());
            
            return ApiResponse.success(savedOrder);
        } catch (Exception e) {
            log.error("從購物車創建訂單失敗: userId={}", userId, e);
            return ApiResponse.error("創建訂單失敗: " + e.getMessage());
        }
    }
    
    /**
     * 直接購買商品（跳過購物車）
     */
    public ApiResponse<Order> createDirectOrder(Long userId, Long productId, Integer quantity,
                                              String receiverName, String receiverPhone, 
                                              String receiverAddress, String remark) {
        try {
            // 檢查商品
            Optional<Product> productOpt = productRepository.findById(productId);
            if (productOpt.isEmpty()) {
                return ApiResponse.error("商品不存在");
            }
            
            Product product = productOpt.get();
            if (product.getStatus() != Product.Status.ON_SHELF) {
                return ApiResponse.error("商品已下架");
            }
            if (product.getStock() < quantity) {
                return ApiResponse.error("庫存不足");
            }
            
            // 計算總金額
            BigDecimal totalAmount = product.getPrice().multiply(BigDecimal.valueOf(quantity));
            
            // 生成訂單號
            String orderNumber = generateOrderNumber();
            
            // 創建訂單
            Order order = new Order(orderNumber, userId, totalAmount, receiverName, receiverPhone, receiverAddress);
            if (remark != null && !remark.trim().isEmpty()) {
                order.setRemark(remark);
            }
            Order savedOrder = orderRepository.save(order);
            
            // 創建訂單項目
            OrderItem orderItem = new OrderItem(
                savedOrder.getId(),
                productId,
                product.getName(),
                product.getMainImageUrl(),
                quantity,
                product.getPrice(),
                product.getBrand(),
                product.getModel()
            );
            orderItemRepository.save(orderItem);
            
            // 減少庫存
            product.setStock(product.getStock() - quantity);
            product.setSoldCount(product.getSoldCount() + quantity);
            productRepository.save(product);
            
            // 創建支付記錄
            Payment payment = new Payment(savedOrder.getId(), Payment.PaymentMethod.ALIPAY, totalAmount);
            paymentRepository.save(payment);
            
            return ApiResponse.success(savedOrder);
        } catch (Exception e) {
            log.error("創建直接訂單失敗: userId={}, productId={}, quantity={}", userId, productId, quantity, e);
            return ApiResponse.error("創建訂單失敗: " + e.getMessage());
        }
    }
    
    /**
     * 獲取用戶訂單列表
     */
    public ApiResponse<PagedResponse<Order>> getUserOrders(Long userId, Pageable pageable) {
        try {
            Page<Order> orderPage = orderRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
            
            PagedResponse<Order> response = new PagedResponse<>(orderPage);
            
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("獲取用戶訂單列表失敗: userId={}", userId, e);
            return ApiResponse.error("獲取訂單列表失敗: " + e.getMessage());
        }
    }
    
    /**
     * 獲取訂單詳情
     */
    public ApiResponse<Order> getOrderDetail(Long userId, Long orderId) {
        try {
            Optional<Order> orderOpt = orderRepository.findByIdWithOrderItemsAndPayment(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            if (!order.getUserId().equals(userId)) {
                return ApiResponse.error("無權限查看此訂單");
            }
            
            return ApiResponse.success(order);
        } catch (Exception e) {
            log.error("獲取訂單詳情失敗: userId={}, orderId={}", userId, orderId, e);
            return ApiResponse.error("獲取訂單詳情失敗: " + e.getMessage());
        }
    }
    
    /**
     * 取消訂單
     */
    public ApiResponse<String> cancelOrder(Long userId, Long orderId) {
        try {
            Optional<Order> orderOpt = orderRepository.findById(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            if (!order.getUserId().equals(userId)) {
                return ApiResponse.error("無權限操作此訂單");
            }
            
            if (order.getStatus() != Order.Status.PENDING_PAYMENT) {
                return ApiResponse.error("只能取消待付款訂單");
            }
            
            // 恢復庫存
            List<OrderItem> orderItems = orderItemRepository.findByOrderIdOrderByCreatedAtAsc(orderId);
            for (OrderItem item : orderItems) {
                Optional<Product> productOpt = productRepository.findById(item.getProductId());
                if (productOpt.isPresent()) {
                    Product product = productOpt.get();
                    product.setStock(product.getStock() + item.getQuantity());
                    product.setSoldCount(product.getSoldCount() - item.getQuantity());
                    productRepository.save(product);
                }
            }
            
            // 更新訂單狀態
            order.setStatus(Order.Status.CANCELLED);
            order.setCancelledAt(LocalDateTime.now());
            orderRepository.save(order);
            
            return ApiResponse.success("訂單已取消");
        } catch (Exception e) {
            log.error("取消訂單失敗: userId={}, orderId={}", userId, orderId, e);
            return ApiResponse.error("取消訂單失敗: " + e.getMessage());
        }
    }
    
    /**
     * 生成訂單號
     */
    private String generateOrderNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.valueOf((int)(Math.random() * 10000));
        return "ORD" + timestamp + String.format("%04d", Integer.parseInt(random));
    }
}
